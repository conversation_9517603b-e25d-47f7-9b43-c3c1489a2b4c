import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { toast } from "react-toastify";
import { useService } from "src/config/context/serviceProvider";
import { getErrorResult } from "src/core/utils/effectErrors";
import type {
	ProductionRequest,
	UpdateProductionRequest,
} from "../service/model/production-request";
import { ProductionRequestRuntime } from "../service/runtime";
import { productionRequestOptions } from "./production-request-options";

export default function useUpdateProductionRequest() {
	const service = useService();
	const { productionRequest } = service;
	const queryClient = useQueryClient();
	const queryKey = productionRequestOptions(service).queryKey;

	return useMutation({
		mutationKey: ["update-production-request"],
		mutationFn: (updateProductionRequest: UpdateProductionRequest) =>
			ProductionRequestRuntime.runPromise(
				productionRequest.update(updateProductionRequest),
			),
		onMutate: async (updateProductionRequest) => {
			await queryClient.cancelQueries({ queryKey });

			const previousProductionRequests = queryClient.getQueryData(queryKey);

			if (previousProductionRequests) {
				queryClient.setQueryData(
					queryKey,
					create(previousProductionRequests, (draft) => {
						const index = draft.findIndex(
							(pr: ProductionRequest) => pr.id === updateProductionRequest.id,
						);
						if (index !== -1) {
							draft[index] = {
								...draft[index],
								code: updateProductionRequest.code,
								expectedDate: updateProductionRequest.expectedDate || null,
								priority: updateProductionRequest.priority,
								state: updateProductionRequest.state,
								requests: updateProductionRequest.requests,
								updatedAt: new Date().toISOString(),
							};
						}
					}),
				);
			}

			return { previousProductionRequests };
		},
		onError: (error, _, context) => {
			queryClient.setQueryData(queryKey, context?.previousProductionRequests);
			const errorResult = getErrorResult(error);
			toast.error(
				`Error al actualizar solicitud de producción: ${errorResult.error.message}`,
			);
		},
		onSuccess: () => {
			toast.success("Solicitud de producción actualizada exitosamente");
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
