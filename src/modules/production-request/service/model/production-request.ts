import { Schema } from "effect";

export const ProductionRequestItem = Schema.Struct({
	id: Schema.String,
	productionRequestId: Schema.String,
	productId: Schema.String,
	quantity: Schema.Number,
});
export type ProductionRequestItem = typeof ProductionRequestItem.Type;

export const ClientResult = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	fatherName: Schema.NullOr(Schema.String),
	motherName: Schema.NullOr(Schema.String),
	clientType: Schema.String,
});
export type ClientResult = typeof ClientResult.Type;

export const ProductionRequest = Schema.Struct({
	id: Schema.String,
	code: Schema.String,
	client: ClientResult,
	expectedDate: Schema.NullOr(Schema.String),
	priority: Schema.String,
	state: Schema.String,
	requests: Schema.Array(ProductionRequestItem),
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
});
export type ProductionRequest = typeof ProductionRequest.Type;

export const CreateProductionRequestItem = Schema.Struct({
	productId: Schema.String,
	quantity: Schema.Number,
});
export type CreateProductionRequestItem =
	typeof CreateProductionRequestItem.Type;

export const CreateProductionRequest = Schema.Struct({
	code: Schema.String,
	clientId: Schema.String,
	expectedDate: Schema.optional(Schema.String),
	priority: Schema.String,
	state: Schema.String,
	requests: Schema.Array(CreateProductionRequestItem),
});
export type CreateProductionRequest = typeof CreateProductionRequest.Type;

export const UpdateProductionRequest = Schema.Struct({
	id: Schema.String,
	code: Schema.String,
	clientId: Schema.String,
	expectedDate: Schema.optional(Schema.String),
	priority: Schema.String,
	state: Schema.String,
	requests: Schema.Array(CreateProductionRequestItem),
});
export type UpdateProductionRequest = typeof UpdateProductionRequest.Type;

export enum ProductionRequestState {
	PENDING = "pending",
	APPROVED = "approved",
	REJECTED = "rejected",
	IN_PROGRESS = "in_progress",
	COMPLETED = "completed",
}

export enum ProductionRequestPriority {
	LOW = "low",
	MEDIUM = "medium",
	HIGH = "high",
	URGENT = "urgent",
}
