import { Schema } from "effect";
import {
	ClientResult,
	CreateProductionRequest,
	CreateProductionRequestItem,
	ProductionRequest,
	ProductionRequestItem,
	UpdateProductionRequest,
} from "../../model/production-request";

export const ProductionRequestItemApi = Schema.Struct({
	id: Schema.String,
	production_request_id: Schema.String,
	product_id: Schema.String,
	quantity: Schema.Number,
});

export const ProductionRequestItemFromApi = Schema.transform(
	ProductionRequestItemApi,
	ProductionRequestItem,
	{
		strict: true,
		decode: (itemApi) => ({
			...itemApi,
			productionRequestId: itemApi.production_request_id,
			productId: itemApi.product_id,
		}),
		encode: (item) => ({
			...item,
			production_request_id: item.productionRequestId,
			product_id: item.productId,
		}),
	},
);

export const ClientResultApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	father_name: Schema.NullOr(Schema.String),
	mother_name: Schema.NullOr(Schema.String),
	client_type: Schema.String,
});

export const ClientResultFromApi = Schema.transform(
	ClientResultApi,
	ClientResult,
	{
		strict: true,
		decode: (clientApi) => ({
			...clientApi,
			fatherName: clientApi.father_name,
			motherName: clientApi.mother_name,
			clientType: clientApi.client_type,
		}),
		encode: (client) => ({
			...client,
			father_name: client.fatherName,
			mother_name: client.motherName,
			client_type: client.clientType,
		}),
	},
);

export const ProductionRequestApi = Schema.Struct({
	id: Schema.String,
	code: Schema.String,
	client: ClientResultFromApi,
	expected_date: Schema.NullOr(Schema.String),
	priority: Schema.String,
	state: Schema.String,
	requests: Schema.Array(ProductionRequestItemFromApi),
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
});

export const ProductionRequestFromApi = Schema.transform(
	ProductionRequestApi,
	ProductionRequest,
	{
		strict: true,
		decode: (requestApi) => ({
			...requestApi,
			expectedDate: requestApi.expected_date,
			createdAt: requestApi.created_at,
			updatedAt: requestApi.updated_at,
		}),
		encode: (request) => ({
			...request,
			expected_date: request.expectedDate,
			created_at: request.createdAt,
			updated_at: request.updatedAt,
		}),
	},
);

export const ProductionRequestListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(ProductionRequestFromApi))),
	Schema.mutable(Schema.Array(ProductionRequest)),
	{
		strict: true,
		decode: (requestApiList) => (requestApiList ? requestApiList : []),
		encode: (requestList) => requestList,
	},
);

export const CreateProductionRequestItemApi = Schema.Struct({
	product_id: Schema.String,
	quantity: Schema.Number,
});

export const CreateProductionRequestItemApiFromCreateProductionRequestItem =
	Schema.transform(
		CreateProductionRequestItem,
		CreateProductionRequestItemApi,
		{
			strict: true,
			decode: (createItem) => ({
				product_id: createItem.productId,
				quantity: createItem.quantity,
			}),
			encode: (createItemApi) => ({
				productId: createItemApi.product_id,
				quantity: createItemApi.quantity,
			}),
		},
	);

export const CreateProductionRequestApi = Schema.Struct({
	code: Schema.String,
	client_id: Schema.String,
	expected_date: Schema.optional(Schema.String),
	priority: Schema.String,
	state: Schema.String,
	requests: Schema.Array(
		CreateProductionRequestItemApiFromCreateProductionRequestItem,
	),
});

export const CreateProductionRequestApiFromCreateProductionRequest =
	Schema.transform(CreateProductionRequest, CreateProductionRequestApi, {
		strict: true,
		decode: (createRequest) => ({
			code: createRequest.code,
			client_id: createRequest.clientId,
			expected_date: createRequest.expectedDate,
			priority: createRequest.priority,
			state: createRequest.state,
			requests: createRequest.requests,
		}),
		encode: (createRequestApi) => ({
			code: createRequestApi.code,
			clientId: createRequestApi.client_id,
			expectedDate: createRequestApi.expected_date,
			priority: createRequestApi.priority,
			state: createRequestApi.state,
			requests: createRequestApi.requests,
		}),
	});

export const UpdateProductionRequestApi = Schema.Struct({
	id: Schema.String,
	code: Schema.String,
	client_id: Schema.String,
	expected_date: Schema.optional(Schema.String),
	priority: Schema.String,
	state: Schema.String,
	requests: Schema.Array(
		CreateProductionRequestItemApiFromCreateProductionRequestItem,
	),
});

export const UpdateProductionRequestApiFromUpdateProductionRequest =
	Schema.transform(UpdateProductionRequest, UpdateProductionRequestApi, {
		strict: true,
		decode: (updateRequest) => ({
			id: updateRequest.id,
			code: updateRequest.code,
			client_id: updateRequest.clientId,
			expected_date: updateRequest.expectedDate,
			priority: updateRequest.priority,
			state: updateRequest.state,
			requests: updateRequest.requests,
		}),
		encode: (updateRequestApi) => ({
			id: updateRequestApi.id,
			code: updateRequestApi.code,
			clientId: updateRequestApi.client_id,
			expectedDate: updateRequestApi.expected_date,
			priority: updateRequestApi.priority,
			state: updateRequestApi.state,
			requests: updateRequestApi.requests,
		}),
	});

export const CreateProductionRequestApiResponse = Schema.String;
