<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Map Point Selection System</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .add-point-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
            margin-bottom: 20px;
        }

        .add-point-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }

        .add-point-btn:active {
            transform: translateY(0);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            width: 90%;
            max-width: 800px;
            height: 80%;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -60%);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 2rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        #map {
            width: 100%;
            height: calc(100% - 80px);
        }

        .map-instructions {
            position: absolute;
            top: 100px;
            left: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            max-width: 250px;
        }

        .points-table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }

        .table-header {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            padding: 20px;
        }

        .table-header h3 {
            font-size: 1.3rem;
            font-weight: 600;
        }

        .points-table {
            width: 100%;
            border-collapse: collapse;
        }

        .points-table th,
        .points-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .points-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }

        .points-table tr:hover {
            background: #f1f5f9;
        }

        .delete-btn {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .delete-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .coordinates {
            font-family: 'Courier New', monospace;
            background: #f3f4f6;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📍 Map Point Selection</h1>
            <p>Click "Add Point" to select locations on the map and build your points table</p>
        </div>
        
        <div class="content">
            <button class="add-point-btn" onclick="openMapModal()">
                ➕ Add Point
            </button>

            <div class="points-table-container">
                <div class="table-header">
                    <h3>Selected Points</h3>
                </div>
                <div id="tableContent">
                    <div class="empty-state">
                        <div class="empty-state-icon">🗺️</div>
                        <h3>No points selected yet</h3>
                        <p>Click "Add Point" to start selecting locations on the map</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Modal -->
    <div id="mapModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">🗺️ Select a Point on the Map</div>
                <button class="close-btn" onclick="closeMapModal()">&times;</button>
            </div>
            <div class="map-instructions">
                <strong>📌 Instructions:</strong><br>
                Click anywhere on the map to select a point. The location details will be automatically added to your table.
            </div>
            <div id="map"></div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        let map;
        let selectedPoints = [];
        let currentMarker = null;
        let leafletLoaded = false;

        // Check if Leaflet is loaded
        function checkLeafletLoaded() {
            return new Promise((resolve) => {
                if (typeof L !== 'undefined') {
                    leafletLoaded = true;
                    resolve(true);
                } else {
                    // Wait a bit and check again
                    setTimeout(() => {
                        if (typeof L !== 'undefined') {
                            leafletLoaded = true;
                            resolve(true);
                        } else {
                            console.error('Leaflet failed to load');
                            resolve(false);
                        }
                    }, 1000);
                }
            });
        }

        async function openMapModal() {
            document.getElementById('mapModal').style.display = 'block';
            
            // Ensure Leaflet is loaded before initializing map
            if (!leafletLoaded) {
                const loaded = await checkLeafletLoaded();
                if (!loaded) {
                    showNotification('Map library failed to load. Please refresh the page.', 'error');
                    closeMapModal();
                    return;
                }
            }
            
            // Initialize map only when modal opens
            setTimeout(() => {
                if (!map) {
                    initializeMap();
                } else {
                    map.invalidateSize();
                }
            }, 100);
        }

        function closeMapModal() {
            document.getElementById('mapModal').style.display = 'none';
        }

        function initializeMap() {
            try {
                if (typeof L === 'undefined') {
                    throw new Error('Leaflet library not loaded');
                }
                
                // Initialize map centered on a default location (you can change this)
                map = L.map('map').setView([40.7128, -74.0060], 10); // New York City as default

                // Add OpenStreetMap tiles
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: ' OpenStreetMap contributors',
                    maxZoom: 19
                }).addTo(map);

                // Add click event listener to the map
                map.on('click', function(e) {
                    const lat = e.latlng.lat;
                    const lng = e.latlng.lng;
                    
                    // Remove previous marker if exists
                    if (currentMarker) {
                        map.removeLayer(currentMarker);
                    }
                    
                    // Add new marker
                    currentMarker = L.marker([lat, lng]).addTo(map);
                    
                    // Get address information using reverse geocoding
                    reverseGeocode(lat, lng);
                });
                
                console.log('Map initialized successfully');
            } catch (error) {
                console.error('Error initializing map:', error);
                showNotification('Error initializing map: ' + error.message, 'error');
            }
        }

        async function reverseGeocode(lat, lng) {
            try {
                // Using Nominatim API for reverse geocoding (free service)
                const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`);
                const data = await response.json();
                
                let address = data.display_name || 'Address not found';
                let city = '';
                let country = '';
                let postalCode = '';
                
                if (data.address) {
                    city = data.address.city || data.address.town || data.address.village || '';
                    country = data.address.country || '';
                    postalCode = data.address.postcode || '';
                }
                
                // Create point object
                const point = {
                    id: Date.now(),
                    latitude: lat.toFixed(6),
                    longitude: lng.toFixed(6),
                    address: address,
                    city: city,
                    country: country,
                    postalCode: postalCode,
                    altitude: 'N/A', // Would need additional API for elevation data
                    timestamp: new Date().toLocaleString()
                };
                
                // Add to points array
                selectedPoints.push(point);
                
                // Update table
                updatePointsTable();
                
                // Close modal
                closeMapModal();
                
                // Show success message
                showNotification('Point added successfully!', 'success');
                
            } catch (error) {
                console.error('Error getting location details:', error);
                showNotification('Error getting location details', 'error');
            }
        }

        function updatePointsTable() {
            const tableContent = document.getElementById('tableContent');
            
            if (selectedPoints.length === 0) {
                tableContent.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">🗺️</div>
                        <h3>No points selected yet</h3>
                        <p>Click "Add Point" to start selecting locations on the map</p>
                    </div>
                `;
                return;
            }
            
            tableContent.innerHTML = `
                <table class="points-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Address</th>
                            <th>Latitude</th>
                            <th>Longitude</th>
                            <th>City</th>
                            <th>Country</th>
                            <th>Postal Code</th>
                            <th>Added</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${selectedPoints.map((point, index) => `
                            <tr>
                                <td><strong>${index + 1}</strong></td>
                                <td title="${point.address}">${truncateText(point.address, 40)}</td>
                                <td><span class="coordinates">${point.latitude}</span></td>
                                <td><span class="coordinates">${point.longitude}</span></td>
                                <td>${point.city || 'N/A'}</td>
                                <td>${point.country || 'N/A'}</td>
                                <td>${point.postalCode || 'N/A'}</td>
                                <td>${point.timestamp}</td>
                                <td>
                                    <button class="delete-btn" onclick="deletePoint(${point.id})">
                                        🗑️ Delete
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        }

        function deletePoint(pointId) {
            if (confirm('Are you sure you want to delete this point?')) {
                selectedPoints = selectedPoints.filter(point => point.id !== pointId);
                updatePointsTable();
                showNotification('Point deleted successfully!', 'success');
            }
        }

        function truncateText(text, maxLength) {
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <span>${message}</span>
                <button onclick="this.parentElement.remove()">×</button>
            `;
            
            // Add notification styles if not already added
            if (!document.getElementById('notificationStyles')) {
                const style = document.createElement('style');
                style.id = 'notificationStyles';
                style.textContent = `
                    .notification {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        padding: 15px 20px;
                        border-radius: 10px;
                        color: white;
                        font-weight: 600;
                        z-index: 10000;
                        display: flex;
                        align-items: center;
                        gap: 15px;
                        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
                        animation: slideInRight 0.3s ease-out;
                    }
                    
                    .notification-success {
                        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    }
                    
                    .notification-error {
                        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                    }
                    
                    .notification button {
                        background: none;
                        border: none;
                        color: white;
                        font-size: 1.2rem;
                        cursor: pointer;
                        padding: 0;
                        border-radius: 50%;
                        width: 20px;
                        height: 20px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    
                    @keyframes slideInRight {
                        from {
                            transform: translateX(100%);
                            opacity: 0;
                        }
                        to {
                            transform: translateX(0);
                            opacity: 1;
                        }
                    }
                `;
                document.head.appendChild(style);
            }
            
            document.body.appendChild(notification);
            
            // Auto remove after 3 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 3000);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('mapModal');
            if (event.target === modal) {
                closeMapModal();
            }
        }

        // Handle escape key to close modal
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeMapModal();
            }
        });

        // Initialize table on page load
        document.addEventListener('DOMContentLoaded', function() {
            updatePointsTable();
        });
    </script>
</body>
</html>